{"name": "onlydiary", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --experimental-https", "dev:fast": "next dev --turbopack --experimental-https --experimental-app-dir", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "postinstall": "mkdir -p public && (cp node_modules/pdfjs-dist/build/pdf.worker.min.mjs public/pdf.worker.min.js 2>/dev/null || cp node_modules/pdfjs-dist/build/pdf.worker.min.js public/pdf.worker.min.js 2>/dev/null || echo 'PDF worker not found, skipping copy')"}, "dependencies": {"@aws-sdk/client-rekognition": "^3.821.0", "@aws-sdk/client-s3": "^3.844.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@stripe/stripe-js": "^7.3.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "epub-gen": "^0.1.0", "epub2": "^3.0.2", "framer-motion": "^12.15.0", "jszip": "^3.10.1", "lucide-react": "^0.511.0", "mammoth": "^1.9.1", "natural": "^8.1.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "readability-score": "^1.0.1", "reading-time": "^1.5.0", "resend": "^4.5.2", "rtf-parser": "^1.3.3", "stripe": "^18.2.1", "tailwind-merge": "^3.3.0", "tus-js-client": "^4.3.1", "web-push": "^3.6.7", "xml2js": "^0.6.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/web-push": "^3.6.4", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}